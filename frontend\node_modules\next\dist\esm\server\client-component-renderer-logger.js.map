{"version": 3, "sources": ["../../src/server/client-component-renderer-logger.ts"], "names": ["clientComponentLoadStart", "clientComponentLoadTimes", "clientComponentLoadCount", "wrapClientComponentLoader", "ComponentMod", "globalThis", "__next_app__", "require", "args", "performance", "now", "startTime", "loadChunk", "getClientComponentLoaderMetrics", "options", "metrics", "undefined", "reset"], "mappings": "AAAA,oDAAoD;AACpD,IAAIA,2BAA2B;AAC/B,IAAIC,2BAA2B;AAC/B,IAAIC,2BAA2B;AAE/B,OAAO,SAASC,0BAA0BC,YAAiB;IACzD,IAAI,CAAE,CAAA,iBAAiBC,UAAS,GAAI;QAClC,OAAOD,aAAaE,YAAY;IAClC;IAEA,OAAO;QACLC,SAAS,CAAC,GAAGC;YACX,IAAIR,6BAA6B,GAAG;gBAClCA,2BAA2BS,YAAYC,GAAG;YAC5C;YAEA,MAAMC,YAAYF,YAAYC,GAAG;YACjC,IAAI;gBACFR,4BAA4B;gBAC5B,OAAOE,aAAaE,YAAY,CAACC,OAAO,IAAIC;YAC9C,SAAU;gBACRP,4BAA4BQ,YAAYC,GAAG,KAAKC;YAClD;QACF;QACAC,WAAW,CAAC,GAAGJ;YACb,MAAMG,YAAYF,YAAYC,GAAG;YACjC,IAAI;gBACFR,4BAA4B;gBAC5B,OAAOE,aAAaE,YAAY,CAACM,SAAS,IAAIJ;YAChD,SAAU;gBACRP,4BAA4BQ,YAAYC,GAAG,KAAKC;YAClD;QACF;IACF;AACF;AAEA,OAAO,SAASE,gCACdC,UAA+B,CAAC,CAAC;IAEjC,MAAMC,UACJf,6BAA6B,IACzBgB,YACA;QACEhB;QACAC;QACAC;IACF;IAEN,IAAIY,QAAQG,KAAK,EAAE;QACjBjB,2BAA2B;QAC3BC,2BAA2B;QAC3BC,2BAA2B;IAC7B;IAEA,OAAOa;AACT"}