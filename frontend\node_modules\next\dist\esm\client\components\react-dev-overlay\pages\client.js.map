{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/pages/client.ts"], "names": ["Bus", "parseStack", "parseComponentStack", "hydrationErrorState", "patchConsoleError", "ACTION_BEFORE_REFRESH", "ACTION_BUILD_ERROR", "ACTION_BUILD_OK", "ACTION_REFRESH", "ACTION_UNHANDLED_ERROR", "ACTION_UNHANDLED_REJECTION", "ACTION_VERSION_INFO", "isRegistered", "stackTraceLimit", "undefined", "onUnhandledError", "ev", "error", "Error", "stack", "message", "match", "warning", "details", "e", "componentStackFrames", "componentStack", "name", "emit", "type", "reason", "frames", "onUnhandledRejection", "register", "limit", "window", "addEventListener", "unregister", "removeEventListener", "onBuildOk", "onBuildError", "onRefresh", "onBeforeRefresh", "onVersionInfo", "versionInfo", "getErrorByType", "getServerError", "default", "ReactDevOverlay"], "mappings": "AAAA,YAAYA,SAAS,QAAO;AAC5B,SAASC,UAAU,QAAQ,iCAAgC;AAC3D,SAASC,mBAAmB,QAAQ,4CAA2C;AAC/E,SACEC,mBAAmB,EACnBC,iBAAiB,QACZ,2CAA0C;AACjD,SACEC,qBAAqB,EACrBC,kBAAkB,EAClBC,eAAe,EACfC,cAAc,EACdC,sBAAsB,EACtBC,0BAA0B,EAC1BC,mBAAmB,QACd,YAAW;AAGlB,oEAAoE;AACpEP;AAEA,IAAIQ,eAAe;AACnB,IAAIC,kBAAsCC;AAE1C,SAASC,iBAAiBC,EAAc;IACtC,MAAMC,QAAQD,sBAAAA,GAAIC,KAAK;IACvB,IAAI,CAACA,SAAS,CAAEA,CAAAA,iBAAiBC,KAAI,KAAM,OAAOD,MAAME,KAAK,KAAK,UAAU;QAC1E,8DAA8D;QAC9D;IACF;IAEA,IACEF,MAAMG,OAAO,CAACC,KAAK,CAAC,sDACpB;QACA,IAAIlB,oBAAoBmB,OAAO,EAAE;YAI7BL,MAAcM,OAAO,GAAG;gBACxB,GAAG,AAACN,MAAcM,OAAO;gBACzB,wEAAwE;gBACxE,GAAGpB,mBAAmB;YACxB;QACF;QACAc,MAAMG,OAAO,IAAK;IACpB;IAEA,MAAMI,IAAIP;IACV,MAAMQ,uBACJ,OAAOtB,oBAAoBuB,cAAc,KAAK,WAC1CxB,oBAAoBC,oBAAoBuB,cAAc,IACtDZ;IAEN,mGAAmG;IACnG,wFAAwF;IACxF,IAAIU,EAAEG,IAAI,KAAK,sBAAsBH,EAAEG,IAAI,KAAK,uBAAuB;QACrE3B,IAAI4B,IAAI,CAAC;YACPC,MAAMpB;YACNqB,QAAQb;YACRc,QAAQ9B,WAAWuB,EAAEL,KAAK;YAC1BM;QACF;IACF;AACF;AAEA,SAASO,qBAAqBhB,EAAyB;IACrD,MAAMc,SAASd,sBAAAA,GAAIc,MAAM;IACzB,IACE,CAACA,UACD,CAAEA,CAAAA,kBAAkBZ,KAAI,KACxB,OAAOY,OAAOX,KAAK,KAAK,UACxB;QACA,8DAA8D;QAC9D;IACF;IAEA,MAAMK,IAAIM;IACV9B,IAAI4B,IAAI,CAAC;QACPC,MAAMnB;QACNoB,QAAQA;QACRC,QAAQ9B,WAAWuB,EAAEL,KAAK;IAC5B;AACF;AAEA,OAAO,SAASc;IACd,IAAIrB,cAAc;QAChB;IACF;IACAA,eAAe;IAEf,IAAI;QACF,MAAMsB,QAAQhB,MAAML,eAAe;QACnCK,MAAML,eAAe,GAAG;QACxBA,kBAAkBqB;IACpB,EAAE,UAAM,CAAC;IAETC,OAAOC,gBAAgB,CAAC,SAASrB;IACjCoB,OAAOC,gBAAgB,CAAC,sBAAsBJ;AAChD;AAEA,OAAO,SAASK;IACd,IAAI,CAACzB,cAAc;QACjB;IACF;IACAA,eAAe;IAEf,IAAIC,oBAAoBC,WAAW;QACjC,IAAI;YACFI,MAAML,eAAe,GAAGA;QAC1B,EAAE,UAAM,CAAC;QACTA,kBAAkBC;IACpB;IAEAqB,OAAOG,mBAAmB,CAAC,SAASvB;IACpCoB,OAAOG,mBAAmB,CAAC,sBAAsBN;AACnD;AAEA,OAAO,SAASO;IACdvC,IAAI4B,IAAI,CAAC;QAAEC,MAAMtB;IAAgB;AACnC;AAEA,OAAO,SAASiC,aAAapB,OAAe;IAC1CpB,IAAI4B,IAAI,CAAC;QAAEC,MAAMvB;QAAoBc;IAAQ;AAC/C;AAEA,OAAO,SAASqB;IACdzC,IAAI4B,IAAI,CAAC;QAAEC,MAAMrB;IAAe;AAClC;AAEA,OAAO,SAASkC;IACd1C,IAAI4B,IAAI,CAAC;QAAEC,MAAMxB;IAAsB;AACzC;AAEA,OAAO,SAASsC,cAAcC,WAAwB;IACpD5C,IAAI4B,IAAI,CAAC;QAAEC,MAAMlB;QAAqBiC;IAAY;AACpD;AAEA,SAASC,cAAc,QAAQ,qCAAoC;AACnE,SAASC,cAAc,QAAQ,sCAAqC;AACpE,SAASC,WAAWC,eAAe,QAAQ,oBAAmB"}