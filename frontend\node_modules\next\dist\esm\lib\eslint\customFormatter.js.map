{"version": 3, "sources": ["../../../src/lib/eslint/customFormatter.ts"], "names": ["bold", "cyan", "gray", "red", "yellow", "path", "MessageSeverity", "pluginCount", "messages", "nextPluginWarningCount", "nextPluginErrorCount", "i", "length", "severity", "ruleId", "includes", "formatMessage", "dir", "filePath", "fileName", "posix", "normalize", "relative", "replace", "startsWith", "output", "message", "line", "column", "toString", "formatResults", "baseDir", "results", "format", "totalNextPluginErrorCount", "totalNextPluginWarningCount", "resultsWithMessages", "filter", "for<PERSON>ach", "res", "map", "join", "outputWithMessages"], "mappings": "AAAA,SAASA,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAEC,MAAM,QAAQ,gBAAe;AAC7D,OAAOC,UAAU,OAAM;;UAGXC;;;GAAAA,oBAAAA;AAsBZ,SAASC,YAAYC,QAAuB;IAI1C,IAAIC,yBAAyB;IAC7B,IAAIC,uBAAuB;IAE3B,IAAK,IAAIC,IAAI,GAAGA,IAAIH,SAASI,MAAM,EAAED,IAAK;QACxC,MAAM,EAAEE,QAAQ,EAAEC,MAAM,EAAE,GAAGN,QAAQ,CAACG,EAAE;QAExC,IAAIG,0BAAAA,OAAQC,QAAQ,CAAC,eAAe;YAClC,IAAIF,gBAAsC;gBACxCJ,0BAA0B;YAC5B,OAAO;gBACLC,wBAAwB;YAC1B;QACF;IACF;IAEA,OAAO;QACLA;QACAD;IACF;AACF;AAEA,SAASO,cACPC,GAAW,EACXT,QAAuB,EACvBU,QAAgB;IAEhB,IAAIC,WAAWd,KAAKe,KAAK,CAACC,SAAS,CACjChB,KAAKiB,QAAQ,CAACL,KAAKC,UAAUK,OAAO,CAAC,OAAO;IAG9C,IAAI,CAACJ,SAASK,UAAU,CAAC,MAAM;QAC7BL,WAAW,OAAOA;IACpB;IAEA,IAAIM,SAAS,OAAOxB,KAAKkB;IAEzB,IAAK,IAAIR,IAAI,GAAGA,IAAIH,SAASI,MAAM,EAAED,IAAK;QACxC,MAAM,EAAEe,OAAO,EAAEb,QAAQ,EAAEc,IAAI,EAAEC,MAAM,EAAEd,MAAM,EAAE,GAAGN,QAAQ,CAACG,EAAE;QAE/Dc,SAASA,SAAS;QAElB,IAAIE,QAAQC,QAAQ;YAClBH,SACEA,SACArB,OAAOuB,KAAKE,QAAQ,MACpB,MACAzB,OAAOwB,OAAOC,QAAQ,MACtB;QACJ;QAEA,IAAIhB,gBAAsC;YACxCY,UAAUrB,OAAOJ,KAAK,cAAc;QACtC,OAAO;YACLyB,UAAUtB,IAAIH,KAAK,YAAY;QACjC;QAEAyB,UAAUC;QAEV,IAAIZ,QAAQ;YACVW,UAAU,OAAOvB,KAAKF,KAAKc;QAC7B;IACF;IAEA,OAAOW;AACT;AAEA,OAAO,SAASK,cACdC,OAAe,EACfC,OAAqB,EACrBC,MAAmC;IAOnC,IAAIC,4BAA4B;IAChC,IAAIC,8BAA8B;IAClC,IAAIC,sBAAsBJ,QAAQK,MAAM,CAAC,CAAC,EAAE7B,QAAQ,EAAE,GAAKA,4BAAAA,SAAUI,MAAM;IAE3E,qDAAqD;IACrDwB,oBAAoBE,OAAO,CAAC,CAAC,EAAE9B,QAAQ,EAAE;QACvC,MAAM+B,MAAMhC,YAAYC;QACxB0B,6BAA6BK,IAAI7B,oBAAoB;QACrDyB,+BAA+BI,IAAI9B,sBAAsB;IAC3D;IAEA,oEAAoE;IACpE,MAAMgB,SAASQ,SACXA,OAAOG,uBACPA,oBACGI,GAAG,CAAC,CAAC,EAAEhC,QAAQ,EAAEU,QAAQ,EAAE,GAC1BF,cAAce,SAASvB,UAAUU,WAElCuB,IAAI,CAAC;IAEZ,OAAO;QACLhB,QAAQA;QACRiB,oBACEN,oBAAoBxB,MAAM,GAAG,IACzBa,SACA,CAAC,IAAI,EAAExB,KACL,QACA,qHAAqH,CAAC,GACxH;QACNiC;QACAC;IACF;AACF"}