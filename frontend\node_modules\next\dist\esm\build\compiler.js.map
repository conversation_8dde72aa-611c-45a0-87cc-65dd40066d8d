{"version": 3, "sources": ["../../src/build/compiler.ts"], "names": ["webpack", "generateStats", "result", "stat", "errors", "warnings", "to<PERSON><PERSON>", "preset", "moduleTrace", "length", "push", "closeCompiler", "compiler", "Promise", "resolve", "reject", "close", "err", "runCompiler", "config", "runWebpackSpan", "inputFileSystem", "fsStartTime", "Date", "now", "run", "stats", "webpackCloseSpan", "<PERSON><PERSON><PERSON><PERSON>", "name", "traceAsyncFn", "then", "reason", "stack", "toString", "message", "details", "Error", "traceFn"], "mappings": "AAAA,SAASA,OAAO,QAAQ,qCAAoC;AAS5D,SAASC,cACPC,MAAsB,EACtBC,IAAmB;IAEnB,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAE,GAAGF,KAAKG,MAAM,CAAC;QACvCC,QAAQ;QACRC,aAAa;IACf;IACA,IAAIJ,UAAUA,OAAOK,MAAM,GAAG,GAAG;QAC/BP,OAAOE,MAAM,CAACM,IAAI,IAAIN;IACxB;IAEA,IAAIC,YAAYA,SAASI,MAAM,GAAG,GAAG;QACnCP,OAAOG,QAAQ,CAACK,IAAI,IAAIL;IAC1B;IAEA,OAAOH;AACT;AAEA,gEAAgE;AAChE,yGAAyG;AACzG,SAASS,cAAcC,QAAkD;IACvE,OAAO,IAAIC,QAAc,CAACC,SAASC;QACjC,4DAA4D;QAC5D,OAAOH,SAASI,KAAK,CAAC,CAACC,MAAcA,MAAMF,OAAOE,OAAOH;IAC3D;AACF;AAEA,OAAO,SAASI,YACdC,MAA6B,EAC7B,EACEC,cAAc,EACdC,eAAe,EAIhB;IAOD,OAAO,IAAIR,QAAQ,CAACC,SAASC;QAC3B,MAAMH,WAAWZ,QAAQmB;QACzB,6CAA6C;QAC7C,IAAIE,iBAAiB;YACnBT,SAASS,eAAe,GAAGA;QAC7B;QACAT,SAASU,WAAW,GAAGC,KAAKC,GAAG;QAC/BZ,SAASa,GAAG,CAAC,CAACR,KAAKS;YACjB,MAAMC,mBAAmBP,eAAeQ,UAAU,CAAC,iBAAiB;gBAClEC,MAAMV,OAAOU,IAAI,IAAI;YACvB;YACAF,iBACGG,YAAY,CAAC,IAAMnB,cAAcC,WACjCmB,IAAI,CAAC;gBACJ,IAAId,KAAK;oBACP,MAAMe,SAASf,IAAIgB,KAAK,IAAIhB,IAAIiB,QAAQ;oBACxC,IAAIF,QAAQ;wBACV,OAAOlB,QAAQ;4BACb;gCACEV,QAAQ;oCAAC;wCAAE+B,SAASH;wCAAQI,SAAS,AAACnB,IAAYmB,OAAO;oCAAC;iCAAE;gCAC5D/B,UAAU,EAAE;gCACZqB;4BACF;4BACAd,SAASS,eAAe;yBACzB;oBACH;oBACA,OAAON,OAAOE;gBAChB,OAAO,IAAI,CAACS,OAAO,MAAM,IAAIW,MAAM;gBAEnC,MAAMnC,SAASyB,iBACZC,UAAU,CAAC,gCACXU,OAAO,CAAC,IACPrC,cAAc;wBAAEG,QAAQ,EAAE;wBAAEC,UAAU,EAAE;wBAAEqB;oBAAM,GAAGA;gBAEvD,OAAOZ,QAAQ;oBAACZ;oBAAQU,SAASS,eAAe;iBAAC;YACnD;QACJ;IACF;AACF"}