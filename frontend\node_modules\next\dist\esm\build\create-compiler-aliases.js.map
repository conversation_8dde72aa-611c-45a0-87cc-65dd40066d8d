{"version": 3, "sources": ["../../src/build/create-compiler-aliases.ts"], "names": ["path", "DOT_NEXT_ALIAS", "PAGES_DIR_ALIAS", "ROOT_DIR_ALIAS", "APP_DIR_ALIAS", "RSC_ACTION_PROXY_ALIAS", "RSC_ACTION_CLIENT_WRAPPER_ALIAS", "RSC_ACTION_VALIDATE_ALIAS", "RSC_ACTION_ENCRYPTION_ALIAS", "defaultOverrides", "NEXT_PROJECT_ROOT", "hasExternalOtelApiPackage", "WEBPACK_LAYERS", "createWebpackAliases", "distDir", "isClient", "isEdgeServer", "isNodeServer", "dev", "config", "pagesDir", "appDir", "dir", "reactProductionProfiling", "hasRewrites", "pageExtensions", "clientResolveRewrites", "require", "resolve", "customAppAliases", "customDocumentAliases", "nextDistPath", "reduce", "prev", "ext", "push", "join", "createNextApiEsmAliases", "undefined", "images", "loaderFile", "next", "getOptimizedModuleAliases", "getReactProfilingInProduction", "getBarrelOptimizationAliases", "experimental", "optimizePackageImports", "dirname", "setimmediate", "createServerOnlyClientOnlyAliases", "isServer", "mapping", "head", "image", "constants", "router", "dynamic", "script", "link", "navigation", "headers", "og", "server", "document", "app", "aliasMap", "key", "value", "Object", "entries", "nextApiFilePath", "createAppRouterApiAliases", "isServerOnlyLayer", "createRSCAliases", "bundledReactChannel", "layer", "alias", "react$", "serverSideRendering", "assign", "reactServerComponents", "unfetch", "url", "packages", "aliases", "mainFields", "pkg", "descriptionFileData", "descriptionFilePath", "field", "hasOwnProperty"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AACvB,SACEC,cAAc,EACdC,eAAe,EACfC,cAAc,EACdC,aAAa,EACbC,sBAAsB,EACtBC,+BAA+B,EAC/BC,yBAAyB,EACzBC,2BAA2B,QAEtB,mBAAkB;AAEzB,SAASC,gBAAgB,QAAQ,yBAAwB;AACzD,SAASC,iBAAiB,EAAEC,yBAAyB,QAAQ,mBAAkB;AAC/E,SAASC,cAAc,QAAQ,mBAAkB;AAMjD,OAAO,SAASC,qBAAqB,EACnCC,OAAO,EACPC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,GAAG,EACHC,MAAM,EACNC,QAAQ,EACRC,MAAM,EACNC,GAAG,EACHC,wBAAwB,EACxBC,WAAW,EAaZ;IACC,MAAMC,iBAAiBN,OAAOM,cAAc;IAC5C,MAAMC,wBAAwBC,QAAQC,OAAO,CAC3C;IAEF,MAAMC,mBAAoC,CAAC;IAC3C,MAAMC,wBAAyC,CAAC;IAEhD,oDAAoD;IACpD,qDAAqD;IACrD,sCAAsC;IACtC,IAAIZ,KAAK;QACP,MAAMa,eAAe,eAAgBf,CAAAA,eAAe,SAAS,EAAC;QAC9Da,gBAAgB,CAAC,CAAC,EAAE3B,gBAAgB,KAAK,CAAC,CAAC,GAAG;eACxCkB,WACAK,eAAeO,MAAM,CAAC,CAACC,MAAMC;gBAC3BD,KAAKE,IAAI,CAACnC,KAAKoC,IAAI,CAAChB,UAAU,CAAC,KAAK,EAAEc,IAAI,CAAC;gBAC3C,OAAOD;YACT,GAAG,EAAE,IACL,EAAE;YACN,CAAC,EAAEF,aAAa,aAAa,CAAC;SAC/B;QACDF,gBAAgB,CAAC,CAAC,EAAE3B,gBAAgB,OAAO,CAAC,CAAC,GAAG;eAC1CkB,WACAK,eAAeO,MAAM,CAAC,CAACC,MAAMC;gBAC3BD,KAAKE,IAAI,CAACnC,KAAKoC,IAAI,CAAChB,UAAU,CAAC,OAAO,EAAEc,IAAI,CAAC;gBAC7C,OAAOD;YACT,GAAG,EAAE,IACL,EAAE;YACN,CAAC,EAAEF,aAAa,eAAe,CAAC;SACjC;QACDD,qBAAqB,CAAC,CAAC,EAAE5B,gBAAgB,UAAU,CAAC,CAAC,GAAG;eAClDkB,WACAK,eAAeO,MAAM,CAAC,CAACC,MAAMC;gBAC3BD,KAAKE,IAAI,CAACnC,KAAKoC,IAAI,CAAChB,UAAU,CAAC,UAAU,EAAEc,IAAI,CAAC;gBAChD,OAAOD;YACT,GAAG,EAAE,IACL,EAAE;YACN,CAAC,EAAEF,aAAa,kBAAkB,CAAC;SACpC;IACH;IAEA,OAAO;QACL,eAAe;QAEf,mDAAmD;QACnD,0CAA0C;QAC1C,GAAIf,eACA;YACE,iBAAiB;YACjB,mBAAmB;YACnB,oBAAoB;YACpB,oBAAoB;YACpB,mBAAmB;YACnB,iBAAiB;YACjB,oBAAoB;YAEpB,GAAGqB,yBAAyB;QAC9B,IACAC,SAAS;QAEb,wBAAwB;QACxB,GAAI,CAAC3B,+BAA+B;YAClC,sBAAsB;QACxB,CAAC;QAED,GAAIQ,OAAOoB,MAAM,CAACC,UAAU,GACxB;YACE,qCAAqCrB,OAAOoB,MAAM,CAACC,UAAU;YAC7D,GAAIxB,gBAAgB;gBAClB,yCAAyCG,OAAOoB,MAAM,CAACC,UAAU;YACnE,CAAC;QACH,IACAF,SAAS;QAEbG,MAAM/B;QAEN,qBAAqBD,gBAAgB,CAAC,mBAAmB;QACzD,eAAeA,gBAAgB,CAAC,aAAa;QAE7C,GAAGoB,gBAAgB;QACnB,GAAGC,qBAAqB;QAExB,GAAIV,WAAW;YAAE,CAAClB,gBAAgB,EAAEkB;QAAS,IAAI,CAAC,CAAC;QACnD,GAAIC,SAAS;YAAE,CAACjB,cAAc,EAAEiB;QAAO,IAAI,CAAC,CAAC;QAC7C,CAAClB,eAAe,EAAEmB;QAClB,CAACrB,eAAe,EAAEa;QAClB,GAAIC,YAAYC,eAAe0B,8BAA8B,CAAC,CAAC;QAC/D,GAAInB,2BAA2BoB,kCAAkC,CAAC,CAAC;QAEnE,wEAAwE;QACxE,6BAA6B;QAC7B,GAAI1B,eACA2B,6BACEzB,OAAO0B,YAAY,CAACC,sBAAsB,IAAI,EAAE,IAElD,CAAC,CAAC;QAEN,CAACvC,0BAA0B,EACzB;QAEF,CAACD,gCAAgC,EAC/B;QAEF,CAACD,uBAAuB,EACtB;QAEF,CAACG,4BAA4B,EAAE;QAE/B,GAAIO,YAAYC,eACZ;YACE,CAACU,sBAAsB,EAAEF,cACrBE,wBAEA;QACN,IACA,CAAC,CAAC;QAEN,kBAAkB1B,KAAKoC,IAAI,CACzBpC,KAAK+C,OAAO,CAACpB,QAAQC,OAAO,CAAC,+BAC7B;QAGFoB,cAAc;IAChB;AACF;AAEA,OAAO,SAASC,kCACdC,QAAiB;IAEjB,OAAOA,WACH;QACE,gBAAgB;QAChB,gBAAgB;QAChB,mCACE;QACF,mCACE;IACJ,IACA;QACE,gBAAgB;QAChB,gBAAgB;QAChB,mCACE;QACF,kCACE;IACJ;AACN;AAEA,OAAO,SAASb;IACd,MAAMc,UAAU;QACdC,MAAM;QACNC,OAAO;QACPC,WAAW;QACXC,QAAQ;QACRC,SAAS;QACTC,QAAQ;QACRC,MAAM;QACNC,YAAY;QACZC,SAAS;QACTC,IAAI;QACJC,QAAQ;QACR,YAAY;QACZC,UAAU;QACVC,KAAK;IACP;IACA,MAAMC,WAAmC,CAAC;IAC1C,sDAAsD;IACtD,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAAClB,SAAU;QAClD,MAAMmB,kBAAkBtE,KAAKoC,IAAI,CAAC1B,mBAAmBwD;QACrDD,QAAQ,CAACK,kBAAkB,MAAM,GAAGH;IACtC;IAEA,OAAOF;AACT;AAEA,OAAO,SAASM,0BAA0BC,iBAA0B;IAClE,MAAMrB,UAAkC;QACtCC,MAAM;QACNI,SAAS;IACX;IAEA,IAAIgB,mBAAmB;QACrBrB,OAAO,CAAC,aAAa,GAAG;IAC1B;IAEA,MAAMc,WAAmC,CAAC;IAC1C,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAAClB,SAAU;QAClD,MAAMmB,kBAAkBtE,KAAKoC,IAAI,CAAC1B,mBAAmBwD;QACrDD,QAAQ,CAACK,kBAAkB,MAAM,GAAGH;IACtC;IACA,OAAOF;AACT;AAEA,OAAO,SAASQ,iBACdC,mBAA2B,EAC3B,EACEC,KAAK,EACL3D,YAAY,EACZO,wBAAwB,EAKzB;IAED,IAAIqD,QAAgC;QAClCC,QAAQ,CAAC,wBAAwB,EAAEH,oBAAoB,CAAC;QACxD,cAAc,CAAC,4BAA4B,EAAEA,oBAAoB,CAAC;QAClE,sBAAsB,CAAC,wBAAwB,EAAEA,oBAAoB,YAAY,CAAC;QAClF,0BAA0B,CAAC,wBAAwB,EAAEA,oBAAoB,gBAAgB,CAAC;QAC1F,qBAAqB,CAAC,4BAA4B,EAAEA,oBAAoB,OAAO,CAAC;QAChF,qBAAqB,CAAC,4BAA4B,EAAEA,oBAAoB,OAAO,CAAC;QAChF,qBAAqB,CAAC,gDAAgD,CAAC;QACvE,0BAA0B,CAAC,qDAAqD,CAAC;QACjF,6BAA6B,CAAC,wDAAwD,CAAC;QACvF,yFAAyF;QACzF,0BAA0B,CAAC,mDAAmD,EAAEA,oBAAoB,GAAG,CAAC;QACxG,6BAA6B,CAAC,sDAAsD,EAAEA,oBAAoB,GAAG,CAAC;QAC9G,iCAAiC;QACjC,oCAAoC,CAAC,2CAA2C,EAAEA,oBAAoB,OAAO,CAAC;QAC9G,yCAAyC,CAAC,2CAA2C,EAAEA,oBAAoB,YAAY,CAAC;QACxH,yCAAyC,CAAC,2CAA2C,EAAEA,oBAAoB,YAAY,CAAC;QACxH,yCAAyC,CAAC,2CAA2C,EAAEA,oBAAoB,YAAY,CAAC;IAC1H;IAEA,IAAI,CAAC1D,cAAc;QACjB,IAAI2D,UAAU/D,eAAekE,mBAAmB,EAAE;YAChDF,QAAQR,OAAOW,MAAM,CAACH,OAAO;gBAC3B,sBAAsB,CAAC,wDAAwD,EAAED,MAAM,kBAAkB,CAAC;gBAC1G,0BAA0B,CAAC,wDAAwD,EAAEA,MAAM,sBAAsB,CAAC;gBAClHE,QAAQ,CAAC,wDAAwD,EAAEF,MAAM,MAAM,CAAC;gBAChF,cAAc,CAAC,wDAAwD,EAAEA,MAAM,UAAU,CAAC;gBAC1F,yCAAyC,CAAC,wDAAwD,EAAEA,MAAM,qCAAqC,CAAC;YAClJ;QACF,OAAO,IAAIA,UAAU/D,eAAeoE,qBAAqB,EAAE;YACzDJ,QAAQR,OAAOW,MAAM,CAACH,OAAO;gBAC3B,sBAAsB,CAAC,wDAAwD,EAAED,MAAM,kBAAkB,CAAC;gBAC1G,0BAA0B,CAAC,wDAAwD,EAAEA,MAAM,sBAAsB,CAAC;gBAClHE,QAAQ,CAAC,wDAAwD,EAAEF,MAAM,MAAM,CAAC;gBAChF,cAAc,CAAC,wDAAwD,EAAEA,MAAM,UAAU,CAAC;gBAC1F,yCAAyC,CAAC,wDAAwD,EAAEA,MAAM,qCAAqC,CAAC;gBAChJ,yCAAyC,CAAC,wDAAwD,EAAEA,MAAM,qCAAqC,CAAC;YAClJ;QACF;IACF;IAEA,IAAI3D,cAAc;QAChB,IAAI2D,UAAU/D,eAAeoE,qBAAqB,EAAE;YAClDJ,KAAK,CACH,SACD,GAAG,CAAC,wBAAwB,EAAEF,oBAAoB,mBAAmB,CAAC;YACvEE,KAAK,CACH,aACD,GAAG,CAAC,4BAA4B,EAAEF,oBAAoB,uBAAuB,CAAC;QACjF,OAAO;YACL,sDAAsD;YACtDE,KAAK,CACH,aACD,GAAG,CAAC,4BAA4B,EAAEF,oBAAoB,sBAAsB,CAAC;QAChF;IACF;IAEA,IAAInD,0BAA0B;QAC5BqD,KAAK,CACH,aACD,GAAG,CAAC,4BAA4B,EAAEF,oBAAoB,UAAU,CAAC;IACpE;IAEAE,KAAK,CACH,gEACD,GAAG,CAAC,uCAAuC,CAAC;IAE7C,OAAOA;AACT;AAEA,oEAAoE;AACpE,qEAAqE;AACrE,OAAO,SAASlC;IACd,OAAO;QACLuC,SAAStD,QAAQC,OAAO,CAAC;QACzB,sBAAsBD,QAAQC,OAAO,CACnC;QAEF,gBAAgBD,QAAQC,OAAO,CAC7B;QAEF,iBAAiBD,QAAQC,OAAO,CAC9B;QAEF,sBAAsBD,QAAQC,OAAO,CACnC;QAEF,gCAAgCD,QAAQC,OAAO,CAC7C;QAEF,0BAA0BD,QAAQC,OAAO,CACvC;QAEF,sBAAsBD,QAAQC,OAAO,CACnC;QAEFsD,KAAKvD,QAAQC,OAAO,CAAC;IACvB;AACF;AAEA,gEAAgE;AAChE,SAASgB,6BAA6BuC,QAAkB;IACtD,MAAMC,UAAqC,CAAC;IAC5C,MAAMC,aAAa;QAAC;QAAU;KAAO;IAErC,KAAK,MAAMC,OAAOH,SAAU;QAC1B,IAAI;YACF,MAAMI,sBAAsB5D,QAAQ,CAAC,EAAE2D,IAAI,aAAa,CAAC;YACzD,MAAME,sBAAsB7D,QAAQC,OAAO,CAAC,CAAC,EAAE0D,IAAI,aAAa,CAAC;YAEjE,KAAK,MAAMG,SAASJ,WAAY;gBAC9B,IAAIE,oBAAoBG,cAAc,CAACD,QAAQ;oBAC7CL,OAAO,CAACE,MAAM,IAAI,GAAGtF,KAAKoC,IAAI,CAC5BpC,KAAK+C,OAAO,CAACyC,sBACbD,mBAAmB,CAACE,MAAM;oBAE5B;gBACF;YACF;QACF,EAAE,OAAM,CAAC;IACX;IAEA,OAAOL;AACT;AACA,SAASzC;IACP,OAAO;QACL,cAAc;IAChB;AACF"}