{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/components/CodeFrame/CodeFrame.tsx"], "names": ["<PERSON><PERSON>", "React", "stripAnsi", "getFrameSource", "useOpenInEditor", "HotlinkedText", "CodeFrame", "stackFrame", "codeFrame", "formattedFrame", "useMemo", "lines", "split", "miniLeadingSpacesLength", "map", "line", "exec", "filter", "Boolean", "v", "pop", "reduce", "c", "n", "isNaN", "length", "Math", "min", "NaN", "a", "indexOf", "substring", "replace", "join", "decoded", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "json", "use_classes", "remove_empty", "open", "file", "lineNumber", "column", "div", "data-nextjs-codeframe", "p", "role", "onClick", "tabIndex", "title", "span", "text", "methodName", "svg", "xmlns", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "path", "d", "polyline", "points", "x1", "y1", "x2", "y2", "pre", "entry", "index", "style", "color", "fg", "undefined", "decoration", "fontWeight", "fontStyle", "content"], "mappings": ";AAAA,OAAOA,WAAW,2BAA0B;AAC5C,YAAYC,WAAW,QAAO;AAE9B,OAAOC,eAAe,gCAA+B;AACrD,SAASC,cAAc,QAAQ,4BAA2B;AAC1D,SAASC,eAAe,QAAQ,mCAAkC;AAClE,SAASC,aAAa,QAAQ,qBAAoB;AAIlD,OAAO,MAAMC,YAAsC,SAASA,UAAU,KAGrE;IAHqE,IAAA,EACpEC,UAAU,EACVC,SAAS,EACV,GAHqE;IAIpE,8CAA8C;IAC9C,MAAMC,iBAAiBR,MAAMS,OAAO,CAAS;QAC3C,MAAMC,QAAQH,UAAUI,KAAK,CAAC;QAE9B,wEAAwE;QACxE,MAAMC,0BAA0BF,MAC7BG,GAAG,CAAC,CAACC,OACJ,oBAAoBC,IAAI,CAACd,UAAUa,WAAW,OAC1C,OACA,oBAAoBC,IAAI,CAACd,UAAUa,QAExCE,MAAM,CAACC,SACPJ,GAAG,CAAC,CAACK,IAAMA,EAAGC,GAAG,IACjBC,MAAM,CAAC,CAACC,GAAGC,IAAOC,MAAMF,KAAKC,EAAEE,MAAM,GAAGC,KAAKC,GAAG,CAACL,GAAGC,EAAEE,MAAM,GAAIG;QAEnE,2EAA2E;QAC3E,8FAA8F;QAC9F,IAAIf,0BAA0B,GAAG;YAC/B,OAAOF,MACJG,GAAG,CAAC,CAACC,MAAMc,IACV,CAAEA,CAAAA,IAAId,KAAKe,OAAO,CAAC,IAAG,IAClBf,KAAKgB,SAAS,CAAC,GAAGF,KAClBd,KAAKgB,SAAS,CAACF,GAAGG,OAAO,CAAC,AAAC,UAAOnB,0BAAwB,KAAI,MAC9DE,MAELkB,IAAI,CAAC;QACV;QACA,OAAOtB,MAAMsB,IAAI,CAAC;IACpB,GAAG;QAACzB;KAAU;IAEd,MAAM0B,UAAUjC,MAAMS,OAAO,CAAC;QAC5B,OAAOV,MAAMmC,UAAU,CAAC1B,gBAAgB;YACtC2B,MAAM;YACNC,aAAa;YACbC,cAAc;QAChB;IACF,GAAG;QAAC7B;KAAe;IAEnB,MAAM8B,OAAOnC,gBAAgB;QAC3BoC,MAAMjC,WAAWiC,IAAI;QACrBC,YAAYlC,WAAWkC,UAAU;QACjCC,QAAQnC,WAAWmC,MAAM;IAC3B;IAEA,gCAAgC;IAChC,qBACE,MAACC;QAAIC,uBAAqB;;0BACxB,KAACD;0BACC,cAAA,MAACE;oBACCC,MAAK;oBACLC,SAASR;oBACTS,UAAU;oBACVC,OAAM;;sCAEN,MAACC;;gCACE/C,eAAeI;gCAAY;gCAAG;8CAC/B,KAACF;oCAAc8C,MAAM5C,WAAW6C,UAAU;;;;sCAE5C,MAACC;4BACCC,OAAM;4BACNC,SAAQ;4BACRC,MAAK;4BACLC,QAAO;4BACPC,aAAY;4BACZC,eAAc;4BACdC,gBAAe;;8CAEf,KAACC;oCAAKC,GAAE;;8CACR,KAACC;oCAASC,QAAO;;8CACjB,KAACjD;oCAAKkD,IAAG;oCAAKC,IAAG;oCAAKC,IAAG;oCAAKC,IAAG;;;;;;;0BAIvC,KAACC;0BACEnC,QAAQpB,GAAG,CAAC,CAACwD,OAAOC,sBACnB,KAACrB;wBAECsB,OAAO;4BACLC,OAAOH,MAAMI,EAAE,GAAG,AAAC,iBAAcJ,MAAMI,EAAE,GAAC,MAAKC;4BAC/C,GAAIL,MAAMM,UAAU,KAAK,SACrB;gCAAEC,YAAY;4BAAI,IAClBP,MAAMM,UAAU,KAAK,WACrB;gCAAEE,WAAW;4BAAS,IACtBH,SAAS;wBACf;kCAECL,MAAMS,OAAO;uBAVT,AAAC,WAAQR;;;;AAgB1B,EAAC"}