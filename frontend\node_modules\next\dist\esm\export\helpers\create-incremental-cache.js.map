{"version": 3, "sources": ["../../../src/export/helpers/create-incremental-cache.ts"], "names": ["path", "IncrementalCache", "hasNextSupport", "nodeFs", "interopDefault", "formatDynamicImportPath", "createIncrementalCache", "cache<PERSON><PERSON><PERSON>", "cacheMaxMemorySize", "fetchCacheKeyPrefix", "distDir", "dir", "enabledDirectories", "experimental", "flushToDisk", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "then", "mod", "default", "incrementalCache", "dev", "requestHeaders", "fetchCache", "maxMemoryCacheSize", "getPrerenderManifest", "version", "routes", "dynamicRoutes", "preview", "previewModeEncryptionKey", "previewModeId", "previewModeSigningKey", "notFoundRoutes", "fs", "pagesDir", "pages", "appDir", "app", "serverDistDir", "join", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minimalMode", "globalThis", "__incrementalCache"], "mappings": "AAEA,OAAOA,UAAU,OAAM;AACvB,SAASC,gBAAgB,QAAQ,qCAAoC;AACrE,SAASC,cAAc,QAAQ,0BAAyB;AACxD,SAASC,MAAM,QAAQ,mCAAkC;AACzD,SAASC,cAAc,QAAQ,4BAA2B;AAC1D,SAASC,uBAAuB,QAAQ,uCAAsC;AAE9E,OAAO,eAAeC,uBAAuB,EAC3CC,YAAY,EACZC,kBAAkB,EAClBC,mBAAmB,EACnBC,OAAO,EACPC,GAAG,EACHC,kBAAkB,EAClBC,YAAY,EACZC,WAAW,EAUZ;IACC,kCAAkC;IAClC,IAAIC;IACJ,IAAIR,cAAc;QAChBQ,eAAeX,eACb,MAAM,MAAM,CAACC,wBAAwBM,KAAKJ,eAAeS,IAAI,CAC3D,CAACC,MAAQA,IAAIC,OAAO,IAAID;IAG9B;IAEA,MAAME,mBAAmB,IAAIlB,iBAAiB;QAC5CmB,KAAK;QACLC,gBAAgB,CAAC;QACjBP;QACAQ,YAAY;QACZC,oBAAoBf;QACpBC;QACAe,sBAAsB,IAAO,CAAA;gBAC3BC,SAAS;gBACTC,QAAQ,CAAC;gBACTC,eAAe,CAAC;gBAChBC,SAAS;oBACPC,0BAA0B;oBAC1BC,eAAe;oBACfC,uBAAuB;gBACzB;gBACAC,gBAAgB,EAAE;YACpB,CAAA;QACAC,IAAI9B;QACJ+B,UAAUtB,mBAAmBuB,KAAK;QAClCC,QAAQxB,mBAAmByB,GAAG;QAC9BC,eAAetC,KAAKuC,IAAI,CAAC7B,SAAS;QAClC8B,iBAAiBzB;QACjB0B,aAAavC;QACbW;IACF;IAEE6B,WAAmBC,kBAAkB,GAAGxB;IAE1C,OAAOA;AACT"}