{"version": 3, "sources": ["../../../../src/client/components/router-reducer/apply-router-state-patch-to-tree.ts"], "names": ["DEFAULT_SEGMENT_KEY", "matchSegment", "addRefreshMarkerToActiveParallelSegments", "applyPatch", "initialTree", "patchTree", "flightSegmentPath", "initialSegment", "initialParallelRoutes", "patchSegment", "patchParallelRoutes", "newParallelRoutes", "key", "isInPatchTreeParallelRoutes", "tree", "applyRouterStatePatchToTree", "flightRouterState", "treePatch", "path", "segment", "parallelRoutes", "url", "refetch", "isRootLayout", "length", "currentSegment", "parallelRouteKey", "lastSegment", "parallelRoutePatch", "slice"], "mappings": "AAIA,SAASA,mBAAmB,QAAQ,8BAA6B;AACjE,SAASC,YAAY,QAAQ,oBAAmB;AAChD,SAASC,wCAAwC,QAAQ,uCAAsC;AAE/F;;CAEC,GACD,SAASC,WACPC,WAA8B,EAC9BC,SAA4B,EAC5BC,iBAAoC;IAEpC,MAAM,CAACC,gBAAgBC,sBAAsB,GAAGJ;IAChD,MAAM,CAACK,cAAcC,oBAAoB,GAAGL;IAE5C,kGAAkG;IAClG,iFAAiF;IACjF,IACEI,iBAAiBT,uBACjBO,mBAAmBP,qBACnB;QACA,OAAOI;IACT;IAEA,IAAIH,aAAaM,gBAAgBE,eAAe;QAC9C,MAAME,oBAA0C,CAAC;QACjD,IAAK,MAAMC,OAAOJ,sBAAuB;YACvC,MAAMK,8BACJ,OAAOH,mBAAmB,CAACE,IAAI,KAAK;YACtC,IAAIC,6BAA6B;gBAC/BF,iBAAiB,CAACC,IAAI,GAAGT,WACvBK,qBAAqB,CAACI,IAAI,EAC1BF,mBAAmB,CAACE,IAAI,EACxBN;YAEJ,OAAO;gBACLK,iBAAiB,CAACC,IAAI,GAAGJ,qBAAqB,CAACI,IAAI;YACrD;QACF;QAEA,IAAK,MAAMA,OAAOF,oBAAqB;YACrC,IAAIC,iBAAiB,CAACC,IAAI,EAAE;gBAC1B;YACF;YAEAD,iBAAiB,CAACC,IAAI,GAAGF,mBAAmB,CAACE,IAAI;QACnD;QAEA,MAAME,OAA0B;YAACP;YAAgBI;SAAkB;QAEnE,8BAA8B;QAC9B,IAAIP,WAAW,CAAC,EAAE,EAAE;YAClBU,IAAI,CAAC,EAAE,GAAGV,WAAW,CAAC,EAAE;QAC1B;QAEA,IAAIA,WAAW,CAAC,EAAE,EAAE;YAClBU,IAAI,CAAC,EAAE,GAAGV,WAAW,CAAC,EAAE;QAC1B;QAEA,IAAIA,WAAW,CAAC,EAAE,EAAE;YAClBU,IAAI,CAAC,EAAE,GAAGV,WAAW,CAAC,EAAE;QAC1B;QAEA,OAAOU;IACT;IAEA,OAAOT;AACT;AAEA;;;;CAIC,GACD,OAAO,SAASU,4BACdT,iBAAoC,EACpCU,iBAAoC,EACpCC,SAA4B,EAC5BC,IAAY;IAEZ,MAAM,CAACC,SAASC,gBAAgBC,KAAKC,SAASC,aAAa,GACzDP;IAEF,eAAe;IACf,IAAIV,kBAAkBkB,MAAM,KAAK,GAAG;QAClC,MAAMV,OAA0BX,WAC9Ba,mBACAC,WACAX;QAGFJ,yCAAyCY,MAAMI;QAE/C,OAAOJ;IACT;IAEA,MAAM,CAACW,gBAAgBC,iBAAiB,GAAGpB;IAE3C,iGAAiG;IACjG,IAAI,CAACL,aAAawB,gBAAgBN,UAAU;QAC1C,OAAO;IACT;IAEA,MAAMQ,cAAcrB,kBAAkBkB,MAAM,KAAK;IAEjD,IAAII;IACJ,IAAID,aAAa;QACfC,qBAAqBzB,WACnBiB,cAAc,CAACM,iBAAiB,EAChCT,WACAX;IAEJ,OAAO;QACLsB,qBAAqBb,4BACnBT,kBAAkBuB,KAAK,CAAC,IACxBT,cAAc,CAACM,iBAAiB,EAChCT,WACAC;QAGF,IAAIU,uBAAuB,MAAM;YAC/B,OAAO;QACT;IACF;IAEA,MAAMd,OAA0B;QAC9BR,iBAAiB,CAAC,EAAE;QACpB;YACE,GAAGc,cAAc;YACjB,CAACM,iBAAiB,EAAEE;QACtB;QACAP;QACAC;KACD;IAED,qCAAqC;IACrC,IAAIC,cAAc;QAChBT,IAAI,CAAC,EAAE,GAAG;IACZ;IAEAZ,yCAAyCY,MAAMI;IAE/C,OAAOJ;AACT"}