{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/icons/CollapseIcon.tsx"], "names": ["CollapseIcon", "collapsed", "svg", "data-nextjs-call-stack-chevron-icon", "data-collapsed", "fill", "height", "width", "shapeRendering", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "viewBox", "style", "transform", "undefined", "path", "d"], "mappings": ";AAAA,OAAO,SAASA,aAAa;IAAA,IAAA,EAAEC,SAAS,EAA2B,GAAtC,mBAAyC,CAAC,IAA1C;IAC3B,qBACE,KAACC;QACCC,qCAAmC;QACnCC,kBAAgBH;QAChBI,MAAK;QACLC,QAAO;QACPC,OAAM;QACNC,gBAAe;QACfC,QAAO;QACPC,eAAc;QACdC,gBAAe;QACfC,aAAY;QACZC,SAAQ;QAGP,GAAI,OAAOZ,cAAc,YACtB;YAAEa,OAAO;gBAAEC,WAAWd,YAAYe,YAAY;YAAgB;QAAE,IAChE,CAAC,CAAC;kBAEN,cAAA,KAACC;YAAKC,GAAE;;;AAGd"}